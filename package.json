{"name": "playbackman", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "biome check .", "check:unsafe": "biome check --write --unsafe .", "check:write": "biome check --write .", "dev": "next dev --turbo", "preview": "next build && next start", "start": "next start", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "typecheck": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@t3-oss/env-nextjs": "^0.12.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.535.0", "mediaelement": "^7.0.7", "next": "^15.2.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "video.js": "^8.23.3", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@tailwindcss/postcss": "^4.0.15", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.14.10", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/video.js": "^7.3.58", "@vitest/ui": "^3.2.4", "jsdom": "^26.1.0", "postcss": "^8.5.3", "tailwindcss": "^4.0.15", "tw-animate-css": "^1.3.6", "typescript": "^5.8.2", "vitest": "^3.2.4"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "pnpm@10.5.2"}