@import "tailwindcss";
@import "tw-animate-css";

/* Video Player Styles */
@import "video.js/dist/video-js.css";
@import "mediaelement/build/mediaelementplayer.min.css";

@custom-variant dark (&:is(.dark *));

@theme {
	--font-sans: var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif,
		"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

@theme inline {
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);
}

:root {
	--radius: 0.625rem;
	--background: oklch(1 0 0);
	--foreground: oklch(0.145 0 0);
	--card: oklch(1 0 0);
	--card-foreground: oklch(0.145 0 0);
	--popover: oklch(1 0 0);
	--popover-foreground: oklch(0.145 0 0);
	--primary: oklch(0.205 0 0);
	--primary-foreground: oklch(0.985 0 0);
	--secondary: oklch(0.97 0 0);
	--secondary-foreground: oklch(0.205 0 0);
	--muted: oklch(0.97 0 0);
	--muted-foreground: oklch(0.556 0 0);
	--accent: oklch(0.97 0 0);
	--accent-foreground: oklch(0.205 0 0);
	--destructive: oklch(0.577 0.245 27.325);
	--border: oklch(0.922 0 0);
	--input: oklch(0.922 0 0);
	--ring: oklch(0.708 0 0);
	--chart-1: oklch(0.646 0.222 41.116);
	--chart-2: oklch(0.6 0.118 184.704);
	--chart-3: oklch(0.398 0.07 227.392);
	--chart-4: oklch(0.828 0.189 84.429);
	--chart-5: oklch(0.769 0.188 70.08);
	--sidebar: oklch(0.985 0 0);
	--sidebar-foreground: oklch(0.145 0 0);
	--sidebar-primary: oklch(0.205 0 0);
	--sidebar-primary-foreground: oklch(0.985 0 0);
	--sidebar-accent: oklch(0.97 0 0);
	--sidebar-accent-foreground: oklch(0.205 0 0);
	--sidebar-border: oklch(0.922 0 0);
	--sidebar-ring: oklch(0.708 0 0);
}

.dark {
	--background: oklch(0.145 0 0);
	--foreground: oklch(0.985 0 0);
	--card: oklch(0.205 0 0);
	--card-foreground: oklch(0.985 0 0);
	--popover: oklch(0.205 0 0);
	--popover-foreground: oklch(0.985 0 0);
	--primary: oklch(0.922 0 0);
	--primary-foreground: oklch(0.205 0 0);
	--secondary: oklch(0.269 0 0);
	--secondary-foreground: oklch(0.985 0 0);
	--muted: oklch(0.269 0 0);
	--muted-foreground: oklch(0.708 0 0);
	--accent: oklch(0.269 0 0);
	--accent-foreground: oklch(0.985 0 0);
	--destructive: oklch(0.704 0.191 22.216);
	--border: oklch(1 0 0 / 10%);
	--input: oklch(1 0 0 / 15%);
	--ring: oklch(0.556 0 0);
	--chart-1: oklch(0.488 0.243 264.376);
	--chart-2: oklch(0.696 0.17 162.48);
	--chart-3: oklch(0.769 0.188 70.08);
	--chart-4: oklch(0.627 0.265 303.9);
	--chart-5: oklch(0.645 0.246 16.439);
	--sidebar: oklch(0.205 0 0);
	--sidebar-foreground: oklch(0.985 0 0);
	--sidebar-primary: oklch(0.488 0.243 264.376);
	--sidebar-primary-foreground: oklch(0.985 0 0);
	--sidebar-accent: oklch(0.269 0 0);
	--sidebar-accent-foreground: oklch(0.985 0 0);
	--sidebar-border: oklch(1 0 0 / 10%);
	--sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
	}
  body {
    @apply bg-background text-foreground;
	}
}

/* Theme-aware Video Player Styles */
@layer components {
  /* Video.js theme integration */
  .video-js {
    @apply bg-background;
  }
  
  .video-js .vjs-control-bar {
    @apply bg-background/90 backdrop-blur-sm;
    border-top: 1px solid hsl(var(--border));
  }
  
  .video-js .vjs-button > .vjs-icon-placeholder:before {
    @apply text-foreground;
  }
  
  .video-js .vjs-play-progress,
  .video-js .vjs-volume-level {
    @apply bg-primary;
  }
  
  .video-js .vjs-progress-holder {
    @apply bg-muted;
  }
  
  .video-js .vjs-load-progress {
    @apply bg-muted-foreground/30;
  }
  
  .video-js .vjs-slider {
    @apply bg-muted;
  }
  
  .video-js .vjs-time-control {
    @apply text-foreground;
  }
  
  .video-js .vjs-remaining-time {
    @apply text-muted-foreground;
  }
  
  /* MediaElement.js theme integration */
  .mejs__container {
    @apply bg-background;
  }
  
  .mejs__controls {
    @apply bg-background/90 backdrop-blur-sm;
    border-top: 1px solid hsl(var(--border));
  }
  
  .mejs__button > button {
    @apply text-foreground;
  }
  
  .mejs__time {
    @apply text-foreground;
  }
  
  .mejs__time-rail .mejs__time-total {
    @apply bg-muted;
  }
  
  .mejs__time-rail .mejs__time-current {
    @apply bg-primary;
  }
  
  .mejs__time-rail .mejs__time-loaded {
    @apply bg-muted-foreground/30;
  }
  
  .mejs__horizontal-volume-slider .mejs__horizontal-volume-total {
    @apply bg-muted;
  }
  
  .mejs__horizontal-volume-slider .mejs__horizontal-volume-current {
    @apply bg-primary;
  }
  
  /* Dark mode specific adjustments */
  .dark .video-js .vjs-big-play-button {
    @apply bg-background/80 border-border;
  }
  
  .dark .video-js .vjs-big-play-button:hover {
    @apply bg-background/90;
  }
  
  .dark .mejs__overlay-button {
    @apply bg-background/80;
  }
  
  /* Responsive player controls */
  @media (max-width: 640px) {
    .video-js .vjs-control-bar {
      font-size: 14px;
    }
    
    .mejs__controls {
      height: 36px;
    }
  }
}
